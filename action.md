好的，我们现在来开发“阶段一：核心体验强化与沉浸式基础”的第三个主要部分：“**榜文任务 (Tasks) - 建立任务激励机制**”。

这个迭代将实现任务的核心生命周期：**创建 (后端)、查看 (前端)、领取 (用户)、完成 (用户，并获得积分)**。我们将构建后端 API，并改造前端的“榜文任务”页面以支持这些功能。

---

### 阶段一：核心体验强化与沉浸式基础

#### 3. 榜文任务 (Tasks) - 建立任务激励机制

**改动点概述：**

1.  **后端 `backend/src/models/task.model.js`：** 确认现有模型满足需求。
2.  **后端 `backend/src/controllers/task.controller.js`：** **新增**控制器文件，实现任务的增、查、改（领取、完成）逻辑。
3.  **后端 `backend/src/routes/task.routes.js`：** **新增**路由文件，定义任务相关的 API 端点。
4.  **后端 `backend/src/routes/index.js`：** 将任务路由添加到主路由中。
5.  **前端 `frontend/src/stores/task.js`：** **新增** Pinia Store 文件，管理任务数据和与后端交互。
6.  **前端 `frontend/src/views/Tasks.vue`：**
    *   从 `taskStore` 获取任务列表。
    *   实现任务的领取和完成功能（通过模态框）。
    *   根据任务状态（未领取、进行中、已完成）显示不同的按钮和样式。
    *   **重要：** 支持筛选“全部榜文”、“我领取的”。
7.  **前端 `frontend/src/views/Dashboard.vue`：** 修改“待办榜文”部分，使其从后端获取真实数据。

---

**详细代码修改：**

### **1. 后端修改**

**1.1. `backend/src/models/task.model.js` (确认/无需修改)**

现有的 `task.model.js` 模型已经包含我们需要的字段 (`title`, `description`, `creator`, `assignee`, `status`, `rewardPoints`等)。所以此文件**无需修改**。

```javascript
// File path: backend/src/models/task.model.js
// 无需修改，保持现有内容
// ...
const Task = mongoose.model('Task', taskSchema);
module.exports = Task;
```

**1.2. `backend/src/controllers/task.controller.js` (新增文件)**

```javascript
// File path: backend/src/controllers/task.controller.js
/**
 * 任务控制器
 * 处理任务的创建、查询、领取、完成等业务逻辑
 */

const Task = require('../models/task.model');
const User = require('../models/user.model');

/**
 * 创建新任务（通常由管理员或系统调用）
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.createTask = async (req, res, next) => {
  try {
    const { title, description, type, category, priority, dueDate, rewardPoints, tags, isPublic } = req.body;
    const creator = req.user.id; // 任务创建者（从认证中间件获取）

    if (!title || !description || !rewardPoints) {
      return res.status(400).json({ success: false, message: '标题、描述和奖励积分是必填项。' });
    }

    const newTask = new Task({
      title,
      description,
      type: type || 'personal', // 默认个人任务
      category: category || 'work', // 默认工作分类
      creator,
      priority: priority || 'medium',
      dueDate,
      rewardPoints,
      tags: tags || [],
      isPublic: isPublic || false,
      status: 'pending' // 默认待处理
    });

    const savedTask = await newTask.save();
    res.status(201).json({ success: true, message: '任务创建成功', task: savedTask });
  } catch (error) {
    console.error('创建任务失败:', error);
    next(error);
  }
};

/**
 * 获取所有任务列表
 * 支持筛选：type, status, assignee, isPublic (如果用户是管理员，可以查看所有)
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getAllTasks = async (req, res, next) => {
  try {
    const { type, status, category, isPublic, assignedToMe } = req.query;
    const userId = req.user.id; // 当前登录用户ID

    let query = {};

    // 默认只显示公开任务，除非明确要求或管理员权限
    if (req.user.role !== 'admin') {
      query.isPublic = true;
    } else if (isPublic !== undefined) { // 如果管理员明确设置isPublic
        query.isPublic = isPublic === 'true';
    }


    if (type) query.type = type;
    if (status) query.status = status;
    if (category) query.category = category;

    // 如果查询“我领取的任务”
    if (assignedToMe === 'true' && userId) {
      query.assignee = userId;
    }

    // 排除已取消的任务（除非明确查询cancelled）
    if (query.status !== 'cancelled') {
        query.status = { $ne: 'cancelled' };
    }

    const tasks = await Task.find(query)
      .populate('creator', 'username nickname avatar') // 填充创建者信息
      .populate('assignee', 'username nickname avatar') // 填充执行者信息
      .sort({ createdAt: -1 }); // 最新创建的在前

    res.json({ success: true, tasks });
  } catch (error) {
    console.error('获取任务列表失败:', error);
    next(error);
  }
};

/**
 * 获取单个任务详情
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.getTaskById = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id;

    const task = await Task.findById(taskId)
      .populate('creator', 'username nickname avatar')
      .populate('assignee', 'username nickname avatar');

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    // 检查任务是否公开，或者当前用户是创建者或执行者或管理员
    if (!task.isPublic && task.creator.toString() !== userId && task.assignee?.toString() !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: '无权查看此任务。' });
    }

    res.json({ success: true, task });
  } catch (error) {
    console.error('获取任务详情失败:', error);
    next(error);
  }
};

/**
 * 领取任务
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.claimTask = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // 当前登录用户ID

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    if (!task.isPublic) {
        return res.status(403).json({ success: false, message: '此任务不可公开领取。' });
    }

    if (task.status !== 'pending') {
      return res.status(400).json({ success: false, message: '该任务已被领取或已完成。' });
    }

    // 领取任务
    task.assignee = userId;
    task.status = 'in_progress';
    await task.save();

    res.json({ success: true, message: '榜文领取成功，开始您的江湖历练吧！', task });
  } catch (error) {
    console.error('领取任务失败:', error);
    next(error);
  }
};

/**
 * 完成任务
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
exports.completeTask = async (req, res, next) => {
  try {
    const { taskId } = req.params;
    const userId = req.user.id; // 当前登录用户ID

    const task = await Task.findById(taskId);

    if (!task) {
      return res.status(404).json({ success: false, message: '任务不存在。' });
    }

    // 只有任务的执行者或管理员才能完成任务
    if (task.assignee?.toString() !== userId && req.user.role !== 'admin') {
      return res.status(403).json({ success: false, message: '无权完成此任务。' });
    }

    if (task.status === 'completed') {
      return res.status(400).json({ success: false, message: '该任务已完成。' });
    }
    if (task.status !== 'in_progress') {
        return res.status(400).json({ success: false, message: '该任务尚未开始或处于无效状态。' });
    }

    // 完成任务
    task.status = 'completed';
    task.completedAt = new Date();
    await task.save();

    // 奖励积分给执行者
    const user = await User.findById(userId);
    if (user) {
      user.stats.points += task.rewardPoints;
      await user.save();
    }

    res.json({ success: true, message: `恭喜您完成任务，获得 ${task.rewardPoints} 内力值！`, task });
  } catch (error) {
    console.error('完成任务失败:', error);
    next(error);
  }
};

// **新增：删除任务（管理员/创建者）**
exports.deleteTask = async (req, res, next) => {
    try {
        const { taskId } = req.params;
        const userId = req.user.id;

        const task = await Task.findById(taskId);
        if (!task) {
            return res.status(404).json({ success: false, message: '任务不存在。' });
        }

        // 只有创建者或管理员才能删除任务
        if (task.creator.toString() !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ success: false, message: '无权删除此任务。' });
        }

        await task.deleteOne(); // 使用 deleteOne 而不是 remove
        res.json({ success: true, message: '任务已成功删除。' });
    } catch (error) {
        console.error('删除任务失败:', error);
        next(error);
    }
};

// **新增：更新任务（管理员/创建者）**
exports.updateTask = async (req, res, next) => {
    try {
        const { taskId } = req.params;
        const userId = req.user.id;
        const updates = req.body;

        const task = await Task.findById(taskId);
        if (!task) {
            return res.status(404).json({ success: false, message: '任务不存在。' });
        }

        // 只有创建者或管理员才能更新任务
        if (task.creator.toString() !== userId && req.user.role !== 'admin') {
            return res.status(403).json({ success: false, message: '无权更新此任务。' });
        }

        // 允许更新的字段
        const allowedUpdates = ['title', 'description', 'type', 'category', 'priority', 'dueDate', 'rewardPoints', 'tags', 'isPublic', 'status', 'assignee'];
        Object.keys(updates).forEach(key => {
            if (allowedUpdates.includes(key)) {
                if (key === 'assignee' && updates[key] === '') { // 允许取消分配
                    task[key] = null;
                } else if (key === 'tags' && !Array.isArray(updates[key])) { // 确保tags是数组
                    task[key] = updates[key].split(',').map(tag => tag.trim());
                }
                else {
                    task[key] = updates[key];
                }
            }
        });

        // 如果手动修改状态为 completed，自动设置 completedAt
        if (updates.status === 'completed' && !task.completedAt) {
            task.completedAt = new Date();
        } else if (updates.status !== 'completed' && task.completedAt) {
            // 如果状态不再是 completed，清空 completedAt
            task.completedAt = null;
        }

        await task.save();
        res.json({ success: true, message: '任务更新成功。', task });
    } catch (error) {
        console.error('更新任务失败:', error);
        next(error);
    }
};
```

**1.3. `backend/src/routes/task.routes.js` (新增文件)**

```javascript
// File path: backend/src/routes/task.routes.js
/**
 * 任务相关路由
 * 处理任务的创建、查询、领取、完成、删除等请求
 */

const express = require('express');
const router = express.Router();
const taskController = require('../controllers/task.controller');
const authMiddleware = require('../middlewares/auth.middleware');

// 所有任务相关的路由都需要身份验证
router.use(authMiddleware.protect);

// 创建任务（可以根据需求调整权限，例如只允许管理员或特定角色）
router.post('/', authMiddleware.restrictTo('admin', 'user'), taskController.createTask); // 暂时允许user创建，后面可再细化

// 获取所有任务列表
router.get('/', taskController.getAllTasks);

// 获取用户分配的任务列表（我领取的任务）
router.get('/my', taskController.getAllTasks); // 复用 getAllTasks，通过 query 参数 assignedToMe=true 实现

// 获取单个任务详情
router.get('/:taskId', taskController.getTaskById);

// 领取任务
router.post('/:taskId/claim', taskController.claimTask);

// 完成任务
router.post('/:taskId/complete', taskController.completeTask);

// 更新任务 (创建者或管理员)
router.put('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.updateTask); // 允许创建者更新

// 删除任务 (创建者或管理员)
router.delete('/:taskId', authMiddleware.restrictTo('admin', 'user'), taskController.deleteTask); // 允许创建者删除

module.exports = router;
```

**1.4. `backend/src/routes/index.js` (修改文件)**

将任务路由添加到主路由中。

```javascript
// File path: backend/src/routes/index.js
/**
 * API 路由主入口
 * 集中管理和导出所有 API 路由
 */

const express = require('express');
const router = express.Router();

// 导入各个模块的路由
const authRoutes = require('./auth.routes');
const clockinRoutes = require('./clockin.routes');
const communityRoutes = require('../community/community.routes.js');
// **新增：导入任务路由**
const taskRoutes = require('./task.routes');

// 路由健康检查 - 简化用于调试
router.get('/health', (req, res) => {
  console.log('[/api/health] 健康检查路由被访问');
  res.status(200).send('API is healthy');
});

// 注册各个模块的路由
router.use('/auth', authRoutes);
router.use('/clockin', clockinRoutes);
router.use('/community', communityRoutes);
// **新增：注册任务路由**
router.use('/tasks', taskRoutes);

// 导出路由实例，供 app.js 使用
module.exports = router;
```

---

### **2. 前端修改**

**2.1. `frontend/src/stores/task.js` (新增文件)**

```javascript
// File path: frontend/src/stores/task.js
import { defineStore } from 'pinia';
import axios from 'axios';
import { useUserStore } from './user'; // 需要用到 userStore 更新积分

// 从环境变量获取后端API基地址
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [], // 任务列表
    loading: false, // 任务列表加载状态
    error: null,    // 任务列表错误信息
    selectedTask: null, // 当前查看的任务详情
    loadingTaskDetail: false, // 任务详情加载状态
    taskDetailError: null,    // 任务详情错误信息
    submittingAction: false, // 领取/完成任务操作状态
    actionError: null,       // 领取/完成任务错误信息
    actionSuccessMessage: null, // 领取/完成任务成功消息
  }),

  getters: {
    // 简化任务状态显示文本
    getTaskStatusText: () => (status) => {
      switch (status) {
        case 'pending': return '待领取';
        case 'in_progress': return '进行中';
        case 'completed': return '已完成';
        case 'cancelled': return '已取消';
        default: return '未知状态';
      }
    },
    // 根据任务状态获取样式类
    getTaskStatusClass: () => (status) => {
      switch (status) {
        case 'pending': return 'status-pending';
        case 'in_progress': return 'status-in-progress';
        case 'completed': return 'status-completed';
        case 'cancelled': return 'status-cancelled';
        default: return '';
      }
    },
    // 任务操作按钮文本
    getTaskActionButtonText: (state) => (task) => {
      const userStore = useUserStore();
      const userId = userStore.userInfo?._id;

      if (task.status === 'completed') return '已完成';
      if (task.status === 'cancelled') return '已取消';
      if (task.status === 'in_progress' && task.assignee?._id === userId) return '完成榜文';
      if (task.status === 'in_progress' && task.assignee?._id !== userId) return '已被领取';
      if (task.status === 'pending') return '领取榜文';
      return '查看';
    },
    // 任务操作按钮是否禁用
    isTaskActionButtonDisabled: (state) => (task) => {
      const userStore = useUserStore();
      const userId = userStore.userInfo?._id;

      if (!userStore.isLoggedIn) return true; // 未登录则禁用所有操作
      if (state.submittingAction) return true; // 操作进行中
      if (task.status === 'completed' || task.status === 'cancelled') return true; // 已完成或已取消
      if (task.status === 'in_progress' && task.assignee?._id !== userId) return true; // 任务已被他人领取
      return false; // 可领取或可完成
    }
  },

  actions: {
    // 获取任务列表
    async fetchTasks(filters = {}) {
      this.loading = true;
      this.error = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.error = '侠士尚未登录，无法查看榜文。';
        this.loading = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/tasks`, {
          params: filters,
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.tasks = response.data.tasks;
          console.log('Store: 任务列表获取成功:', this.tasks.length, '条');
        } else {
          this.error = response.data.message || '获取任务列表失败。';
          console.error('Store: 获取任务列表失败:', this.error);
        }
      } catch (err) {
        console.error('Store: 获取任务列表请求错误:', err);
        this.error = err.response?.data?.message || '获取任务列表错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
      } finally {
        this.loading = false;
      }
    },

    // 获取单个任务详情
    async fetchTaskById(taskId) {
      this.loadingTaskDetail = true;
      this.taskDetailError = null;
      this.selectedTask = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.taskDetailError = '侠士尚未登录，无法查看榜文详情。';
        this.loadingTaskDetail = false;
        return;
      }

      try {
        const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.selectedTask = response.data.task;
          console.log('Store: 任务详情获取成功:', this.selectedTask.title);
        } else {
          this.taskDetailError = response.data.message || '获取任务详情失败。';
          console.error('Store: 获取任务详情失败:', this.taskDetailError);
        }
      } catch (err) {
        console.error('Store: 获取任务详情请求错误:', err);
        this.taskDetailError = err.response?.data?.message || '获取任务详情错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
      } finally {
        this.loadingTaskDetail = false;
      }
    },

    // 领取任务
    async claimTask(taskId) {
      this.submittingAction = true;
      this.actionError = null;
      this.actionSuccessMessage = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.actionError = '侠士尚未登录，无法领取榜文。';
        this.submittingAction = false;
        return { success: false, message: this.actionError };
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/claim`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.actionSuccessMessage = response.data.message;
          // 更新 tasks 列表中的对应任务状态
          const index = this.tasks.findIndex(t => t._id === taskId);
          if (index !== -1) {
            this.tasks[index] = { ...this.tasks[index], ...response.data.task };
          }
          // 如果当前正在查看此任务详情，也更新
          if (this.selectedTask && this.selectedTask._id === taskId) {
            this.selectedTask = { ...this.selectedTask, ...response.data.task };
          }
          console.log('Store: 榜文领取成功:', response.data.message);
          return { success: true, message: this.actionSuccessMessage };
        } else {
          this.actionError = response.data.message || '领取榜文失败。';
          console.error('Store: 领取榜文失败:', this.actionError);
          return { success: false, message: this.actionError };
        }
      } catch (err) {
        console.error('Store: 领取榜文请求错误:', err);
        this.actionError = err.response?.data?.message || '领取榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.actionError };
      } finally {
        this.submittingAction = false;
      }
    },

    // 完成任务
    async completeTask(taskId) {
      this.submittingAction = true;
      this.actionError = null;
      this.actionSuccessMessage = null;
      const userStore = useUserStore();
      const token = userStore.token;

      if (!token) {
        this.actionError = '侠士尚未登录，无法完成榜文。';
        this.submittingAction = false;
        return { success: false, message: this.actionError };
      }

      try {
        const response = await axios.post(`${API_BASE_URL}/api/tasks/${taskId}/complete`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data && response.data.success) {
          this.actionSuccessMessage = response.data.message;
          // 更新 tasks 列表中的对应任务状态
          const index = this.tasks.findIndex(t => t._id === taskId);
          if (index !== -1) {
            this.tasks[index] = { ...this.tasks[index], ...response.data.task };
          }
          // 如果当前正在查看此任务详情，也更新
          if (this.selectedTask && this.selectedTask._id === taskId) {
            this.selectedTask = { ...this.selectedTask, ...response.data.task };
          }
          // 更新用户积分（前端自行同步或重新获取用户信息）
          await userStore.fetchUserInfo(); // 重新获取用户最新信息，包括积分
          console.log('Store: 榜文完成成功:', response.data.message);
          return { success: true, message: this.actionSuccessMessage };
        } else {
          this.actionError = response.data.message || '完成榜文失败。';
          console.error('Store: 完成榜文失败:', this.actionError);
          return { success: false, message: this.actionError };
        }
      } catch (err) {
        console.error('Store: 完成榜文请求错误:', err);
        this.actionError = err.response?.data?.message || '完成榜文错误，请稍后再试。';
        if (err.response && err.response.status === 401) userStore.setToken(null);
        return { success: false, message: this.actionError };
      } finally {
        this.submittingAction = false;
      }
    },

    // 清除选中的任务详情
    clearSelectedTask() {
      this.selectedTask = null;
      this.taskDetailError = null;
      this.actionError = null;
      this.actionSuccessMessage = null;
    }
  },
});
```

**2.2. `frontend/src/views/Tasks.vue` (修改文件)**

```vue
<template>
  <div class="tasks-page ancient-pixel-container">
    <!-- Main Page Title -->
    <h1>榜文任务 · 江湖历练</h1>

    <!-- Top Filter/Category Tags -->
    <div class="filter-tags">
      <button
        v-for="tag in filterTags"
        :key="tag.value"
        class="tag-button"
        :class="{ 'active': activeTag === tag.value }"
        @click="setActiveTag(tag.value)"
      >
        {{ tag.label }}
      </button>
    </div>

    <!-- Task List Area -->
    <div v-if="loading" class="loading-indicator">榜文载入中...</div>
    <div v-else-if="error" class="error-text">{{ error }}</div>
    <div v-else class="task-list-container">
      <div
        v-for="task in tasks"
        :key="task._id"
        class="card task-card"
        :class="getTaskStatusClass(task.status)"
        @click="viewTaskDetail(task._id)"
      >
        <div class="task-icon">{{ getTaskIcon(task.category) }}</div>
        <div class="task-details">
          <div class="task-title">{{ task.title }}</div>
          <div class="task-description">{{ task.description }}</div>
        </div>
        <div class="task-meta">
          <div class="task-reward">💰 +{{ task.rewardPoints }} 内力值</div>
          <div class="task-status-display">状态: <span :class="getTaskStatusClass(task.status)">{{ getTaskStatusText(task.status) }}</span></div>
          <button
            class="claim-button pixel-button primary"
            :class="{ 'claimed': task.status !== 'pending' && task.status !== 'in_progress', 'status-completed': task.status === 'completed' }"
            @click.stop="handleTaskAction(task)"
            :disabled="isTaskActionButtonDisabled(task)"
          >
            {{ getTaskActionButtonText(task) }}
          </button>
        </div>
      </div>

      <div v-if="tasks.length === 0 && !loading" class="empty-tasks ancient-text">
          <p>当前分类下没有榜文哦，去看看别的吧！</p>
      </div>
    </div>

    <!-- 任务详情模态框 -->
    <div v-if="selectedTask" class="modal-overlay" @click.self="closeTaskDetailModal">
      <div class="modal-content card">
        <button class="modal-close-button" @click="closeTaskDetailModal">X</button>
        <div v-if="loadingTaskDetail" class="loading-indicator">榜文详情载入中...</div>
        <div v-else-if="taskDetailError" class="error-text">{{ taskDetailError }}</div>
        <div v-else-if="selectedTask">
          <h2 class="task-detail-title">{{ selectedTask.title }}</h2>
          <p class="task-detail-meta">
            发布者: {{ selectedTask.creator?.nickname || selectedTask.creator?.username || '佚名' }} |
            发布时间: {{ new Date(selectedTask.createdAt).toLocaleDateString() }}
          </p>
          <div class="task-detail-body">
            <p>任务描述: {{ selectedTask.description }}</p>
            <p>奖励内力值: 💰 {{ selectedTask.rewardPoints }}</p>
            <p v-if="selectedTask.assignee">领取者: {{ selectedTask.assignee?.nickname || selectedTask.assignee?.username || '佚名' }}</p>
            <p>截止日期: {{ selectedTask.dueDate ? new Date(selectedTask.dueDate).toLocaleDateString() : '无限制' }}</p>
            <p>当前状态: <span :class="getTaskStatusClass(selectedTask.status)">{{ getTaskStatusText(selectedTask.status) }}</span></p>
          </div>

          <div v-if="actionSuccessMessage" class="success-message">{{ actionSuccessMessage }}</div>
          <div v-if="actionError" class="error-message">{{ actionError }}</div>

          <div class="task-detail-actions">
            <button
              v-if="selectedTask.status === 'pending'"
              @click="claimTask(selectedTask._id)"
              class="pixel-button primary"
              :disabled="submittingAction || !isLoggedIn"
            >
              {{ submittingAction ? '领取中...' : '领取榜文' }}
            </button>
            <button
              v-if="selectedTask.status === 'in_progress' && selectedTask.assignee?._id === currentUser._id"
              @click="completeTask(selectedTask._id)"
              class="pixel-button primary"
              :disabled="submittingAction"
            >
              {{ submittingAction ? '完成中...' : '完成榜文' }}
            </button>
            <button
              v-if="selectedTask.status === 'in_progress' && selectedTask.assignee?._id !== currentUser._id"
              class="pixel-button secondary"
              disabled
            >
              已被他人领取
            </button>
            <button
              v-if="selectedTask.status === 'completed'"
              class="pixel-button secondary status-completed"
              disabled
            >
              已完成
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useTaskStore } from '@/stores/task';
import { useUserStore } from '@/stores/user'; // 引入 userStore 获取当前用户ID
import { storeToRefs } from 'pinia';

const taskStore = useTaskStore();
const userStore = useUserStore(); // 初始化 userStore

const {
  tasks,
  loading,
  error,
  selectedTask,
  loadingTaskDetail,
  taskDetailError,
  submittingAction,
  actionError,
  actionSuccessMessage,
  getTaskStatusText,
  getTaskStatusClass,
  getTaskActionButtonText,
  isTaskActionButtonDisabled
} = storeToRefs(taskStore);

const { isLoggedIn, userInfo: currentUser } = storeToRefs(userStore); // 获取登录状态和当前用户信息

// Filter tags
const filterTags = ref([
  { label: '全部榜文', value: 'all' },
  { label: '门派推荐', value: 'public' }, // 映射到后端isPublic
  { label: '我领取的', value: 'my-tasks' }, // 映射到后端assignedToMe
]);
const activeTag = ref('all');

// Function to set active tag and refetch tasks
const setActiveTag = (tagValue) => {
  activeTag.value = tagValue;
  let filters = {};
  if (tagValue === 'public') {
    filters.isPublic = true;
  } else if (tagValue === 'my-tasks') {
    filters.assignedToMe = true;
  }
  taskStore.fetchTasks(filters);
};

// Map task categories to icons
const getTaskIcon = (category) => {
  switch (category) {
    case 'work': return '💼'; // Briefcase
    case 'study': return '📚'; // Books
    case 'design': return '🎨'; // Art palette
    case 'development': return '💻'; // Laptop
    case 'document': return '📄'; // Document
    case 'other': return '✨'; // Sparkles
    default: return '❓'; // Question mark
  }
};


// Task Detail Modal Logic
const viewTaskDetail = async (taskId) => {
  await taskStore.fetchTaskById(taskId);
};

const closeTaskDetailModal = () => {
  taskStore.clearSelectedTask();
};

// Task Actions (Claim / Complete)
const handleTaskAction = async (task) => {
  if (!isLoggedIn.value) {
    alert('请先入世修行，方可领取或完成榜文！'); // Consider routing to login
    return;
  }

  if (task.status === 'pending') {
    const result = await taskStore.claimTask(task._id);
    if (result.success) {
      // 可以在此处显示一个短暂的成功提示
      console.log('领取成功!');
      // 模态框打开的情况下，自动刷新模态框里的任务状态
      if (selectedTask.value && selectedTask.value._id === task._id) {
          await taskStore.fetchTaskById(task._id);
      }
    } else {
      console.error('领取失败:', result.message);
    }
  } else if (task.status === 'in_progress' && task.assignee?._id === currentUser.value._id) {
    const result = await taskStore.completeTask(task._id);
    if (result.success) {
      console.log('完成成功!');
      // 模态框打开的情况下，自动刷新模态框里的任务状态
      if (selectedTask.value && selectedTask.value._id === task._id) {
          await taskStore.fetchTaskById(task._id);
      }
    } else {
      console.error('完成失败:', result.message);
    }
  }
};


onMounted(() => {
  taskStore.fetchTasks(); // Fetch all tasks on mount
});

// Watch for currentUser changes to refetch tasks when login status changes
watch(currentUser, (newVal, oldVal) => {
  // Only refetch if login status actually changed (e.g., from null to user object, or vice-versa)
  if (newVal?._id !== oldVal?._id) {
    taskStore.fetchTasks();
  }
});

</script>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss'; // 引入新的 SCSS 文件

.tasks-page {
  padding: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  background-color: var(--color-ancient-paper);
}

h1 {
  color: var(--color-ancient-ink);
  margin-bottom: 20px;
  text-align: left;
  font-size: 2em;
  font-family: 'ZCOOL KuaiLe', serif;
}

.filter-tags {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.tag-button {
  @extend .pixel-button; // Extend base pixel button styles
  background-color: var(--color-ancient-paper); // Default background for tags
  color: var(--color-ancient-dark-brown);
  padding: 8px 15px;
  font-size: 0.9em;
  box-shadow: 3px 3px 0px var(--color-ancient-light-brown); // Pixel shadow for tags

  &:hover {
    background-color: var(--color-ancient-highlight);
    transform: translate(-1px, -1px);
    box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  }
}

.tag-button.active {
  background-color: var(--color-ancient-jade); // Active tag background
  color: var(--color-ancient-ink); // Active tag text color
  border-color: var(--color-ancient-jade-dark);
  box-shadow: 4px 4px 0px var(--color-ancient-dark-brown);
}

.loading-indicator, .error-text {
  text-align: center;
  color: var(--color-ancient-light-brown);
  font-style: italic;
  padding: 20px 0;
  font-family: 'Noto Serif SC', serif;
}

.error-text {
  color: var(--color-ancient-blood-red);
  font-weight: bold;
}

.task-list-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.task-card {
  @extend .card; // Extend base card styles
  display: flex;
  align-items: flex-start; // Align items to the top
  gap: 15px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s, background-color 0.2s;
  position: relative;
  padding: 20px;

  &:hover {
    transform: translate(-3px, -3px);
    box-shadow: 9px 9px 0px var(--color-ancient-light-brown);
    background-color: var(--color-ancient-highlight);
  }

  // Task Status Specific Styles
  &.status-pending {
    border-color: var(--color-ancient-gold);
    .task-icon { color: var(--color-ancient-gold); }
  }
  &.status-in-progress {
    border-color: var(--color-ancient-jade);
    .task-icon { color: var(--color-ancient-jade); }
  }
  &.status-completed {
    border-color: var(--color-ancient-stone-gray);
    background-color: var(--color-ancient-stone-gray-light);
    color: var(--color-ancient-stone-gray-dark);
    cursor: default;
    .task-icon { color: var(--color-ancient-stone-gray); }
    .task-title, .task-description {
      text-decoration: line-through;
      color: var(--color-ancient-stone-gray-dark);
    }
    .claim-button {
      background-color: var(--color-ancient-stone-gray) !important; // Override primary/secondary
      color: var(--color-neutral-white) !important;
      border-color: var(--color-ancient-stone-gray-dark) !important;
      box-shadow: none !important;
      transform: none !important;
      cursor: not-allowed !important;
      &:hover { opacity: 0.9; }
    }
  }
  &.status-cancelled {
    border-color: var(--color-ancient-blood-red);
    background-color: #fcebeb; // Lighter red background
    color: var(--color-ancient-blood-red-dark);
    cursor: default;
    .task-icon { color: var(--color-ancient-blood-red); }
    .task-title, .task-description {
      text-decoration: line-through;
      color: var(--color-ancient-blood-red-dark);
    }
     .claim-button {
      background-color: var(--color-ancient-blood-red) !important;
      color: var(--color-neutral-white) !important;
      border-color: var(--color-ancient-blood-red-dark) !important;
      box-shadow: none !important;
      transform: none !important;
      cursor: not-allowed !important;
      &:hover { opacity: 0.9; }
    }
  }
}

.task-icon {
  font-size: 36px;
  flex-shrink: 0;
  line-height: 1; // Prevent extra spacing
}

.task-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-family: 'Noto Serif SC', serif;
}

.task-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 1.1em;
  font-weight: bold;
  color: var(--color-ancient-ink);
}

.task-description {
  font-family: 'Noto Serif SC', serif;
  font-size: 0.9em;
  color: var(--color-ancient-dark-brown);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

.task-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
  flex-shrink: 0;
  font-family: 'Pixelify Sans', monospace;
  text-align: right; // Align all meta text right
}

.task-reward,
.task-status-display {
  font-size: 0.9em;
  color: var(--color-ancient-dark-brown);
  display: flex;
  align-items: center;
  justify-content: flex-end; // Align content within flex item to the right
  width: 100%; // Take full width of meta container

  span {
    margin-left: 5px; // Space after text for icon
    font-weight: bold;
  }
  .status-pending { color: var(--color-ancient-gold); }
  .status-in-progress { color: var(--color-ancient-jade); }
  .status-completed { color: #888; } // Lighter gray for completed text
  .status-cancelled { color: var(--color-ancient-blood-red); }
}

.claim-button {
  @extend .pixel-button; // Extend base pixel button
  font-size: 0.9em;
  padding: 8px 15px;
  width: 100%; // Make button fill width
  white-space: nowrap; // Prevent text wrapping
}

.empty-tasks {
  text-align: center;
  color: var(--color-ancient-light-brown);
  padding: 40px 20px;
  grid-column: 1 / -1; // Span across all columns in grid
  font-family: 'Noto Serif SC', serif;
}

/* Modal Styles (Task Detail) */
.modal-overlay {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 20;
}

.modal-content {
  @extend .card; // Extend base card styles
  background-color: var(--color-ancient-paper);
  padding: 30px;
  width: 90%;
  max-width: 600px;
  position: relative;
  max-height: 90vh;
  overflow-y: auto;

  .modal-close-button {
    position: absolute;
    top: 10px; right: 10px;
    background: none; border: none;
    font-size: 20px;
    cursor: pointer;
    color: var(--color-ancient-dark-brown);
    &:hover { color: #555; }
  }

  .task-detail-title {
    font-family: 'ZCOOL KuaiLe', serif;
    font-size: 1.8em;
    color: var(--color-ancient-ink);
    margin-top: 0;
    margin-bottom: 10px;
    text-align: center;
  }

  .task-detail-meta {
    font-family: 'Pixelify Sans', monospace;
    font-size: 0.9em;
    color: var(--color-ancient-light-brown);
    margin-bottom: 20px;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 10px;
    text-align: center;
  }

  .task-detail-body {
    font-family: 'Noto Serif SC', serif;
    font-size: 1em;
    color: var(--color-ancient-dark-brown);
    line-height: 1.6;
    margin-bottom: 20px;

    p { margin-bottom: 8px; }
    span { font-weight: bold; color: var(--color-ancient-ink); }
    .status-pending { color: var(--color-ancient-gold); }
    .status-in-progress { color: var(--color-ancient-jade); }
    .status-completed { color: #888; }
    .status-cancelled { color: var(--color-ancient-blood-red); }
  }

  .task-detail-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
  }
   .success-message, .error-message {
      text-align: center;
      padding: 10px;
      margin-bottom: 15px;
      border-radius: 0;
      font-size: 0.9em;
      font-family: 'Pixelify Sans', monospace;
      border-width: 2px;
      border-style: solid;
   }
   .success-message {
       color: var(--color-ancient-jade-dark);
       background-color: var(--color-ancient-paper);
       border-color: var(--color-ancient-jade);
   }
   .error-message {
       color: var(--color-ancient-blood-red);
       background-color: var(--color-ancient-paper);
       border-color: var(--color-ancient-blood-red);
   }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tasks-page {
    padding: 15px;
  }
  h1 { font-size: 1.8em; text-align: center; }
  .filter-tags { justify-content: center; }
  .tag-button { padding: 6px 12px; font-size: 0.8em; }
  .task-list-container { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
  .task-card { flex-direction: column; align-items: flex-start; gap: 10px; padding: 15px; }
  .task-icon { font-size: 30px; }
  .task-details { width: 100%; }
  .task-title { font-size: 1em; }
  .task-description { font-size: 0.8em; }
  .task-meta { flex-direction: row; justify-content: space-between; align-items: center; gap: 10px; width: 100%; }
  .claim-button { padding: 6px 12px; font-size: 0.8em; }
  .modal-content { max-width: 450px; padding: 20px; }
  .task-detail-title { font-size: 1.5em; }
  .task-detail-meta, .task-detail-body { font-size: 0.9em; }
}

@media (max-width: 480px) {
  .tasks-page { padding: 10px; }
  h1 { font-size: 1.5em; margin-bottom: 15px; }
  .task-list-container { grid-template-columns: 1fr; }
  .task-card { padding: 10px; }
  .task-icon { font-size: 24px; }
  .task-meta { flex-direction: column; align-items: flex-start; gap: 5px; }
  .claim-button { font-size: 0.85em; padding: 5px 10px; }
  .modal-content { padding: 15px; }
  .task-detail-title { font-size: 1.2em; }
  .task-detail-meta, .task-detail-body { font-size: 0.85em; }
  .task-detail-actions { gap: 8px; }
  .modal-content .pixel-button { font-size: 0.9em; padding: 8px 12px; }
}
</style>
```

**2.3. `frontend/src/views/Dashboard.vue` (修改文件)**

更新“待办榜文”部分，使其从后端获取真实数据。

```vue
<script setup>
// File path: frontend/src/views/Dashboard.vue
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/stores/user';
import { useAttendanceStore } from '@/stores/attendance';
import { useTaskStore } from '@/stores/task'; // **新增：引入 taskStore**
import { storeToRefs } from 'pinia';

const router = useRouter();
const userStore = useUserStore();
const attendanceStore = useAttendanceStore();
const taskStore = useTaskStore(); // **新增：初始化 taskStore**

// User Info from userStore
const userInfo = computed(() => userStore.userInfo);
const loadingUser = computed(() => userStore.loading);
const userError = computed(() => userStore.error);
const fullAvatarUrl = computed(() => userStore.fullAvatarUrl);

// Attendance Info from attendanceStore
const {
  clockedIn,
  clockInTime,
  clockOutTime,
  isClockingIn,
  todayHours,
  loadingStats: loadingAttendance,
  statsError: attendanceError,
} = storeToRefs(attendanceStore);

// **新增：Task Info from taskStore**
const {
  tasks: allTasks, // 获取所有任务，因为 dashboard 只展示一小部分
  loading: loadingTasks,
  error: tasksError,
  getTaskStatusText, // 引入 getter
  getTaskStatusClass // 引入 getter
} = storeToRefs(taskStore);


const 修行时长 = computed(() => {
  const currentTodayHours = typeof todayHours.value === 'number' ? todayHours.value : 0;

  if (clockOutTime.value && clockInTime.value) {
    const hours = Math.floor(currentTodayHours);
    const minutes = Math.round((currentTodayHours - hours) * 60);
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
  }

  if (clockInTime.value && typeof clockInTime.value === 'string' && clockInTime.value.includes(':')) {
    const [startHours, startMinutes] = clockInTime.value.split(':').map(Number);
    const startTime = new Date();
    startTime.setHours(startHours, startMinutes, 0, 0);

    const now = new Date();
    if (isNaN(startTime.getTime()) || now < startTime) {
      return '计时中...';
    }

    const diffMs = now - startTime;
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${String(diffHours).padStart(2, '0')}:${String(diffMinutes).padStart(2, '0')} (进行中)`;
  }

  return '00:00';
});


const clockInButtonText = computed(() => {
  if (!clockedIn.value) return '上班打卡';
  if (clockedIn.value && !clockOutTime.value) return '下班打卡';
  return '今日已打卡';
});

const canClockIn = computed(() => !clockedIn.value && !isClockingIn.value);
const canClockOut = computed(() => clockedIn.value && !clockOutTime.value && !isClockingIn.value);


const clockAnimationActive = ref(false);

const handleClockAction = async (type) => {
  if (type === 'in' && !canClockIn.value) {
    return;
  }
  if (type === 'out' && !canClockOut.value) {
    return;
  }

  const result = await attendanceStore.performClockIn(type);
  if (result.success) {
    clockAnimationActive.value = true;
    setTimeout(() => {
      clockAnimationActive.value = false;
    }, 600);
  } else {
    alert(result.message || '操作失败');
  }
};


// **修改：从 taskStore 中获取待办任务**
const pendingTasks = computed(() => {
  const userId = userInfo.value?._id;
  if (!userId) return []; // If user not logged in, no tasks
  // Filter for tasks assigned to current user and not yet completed/cancelled
  return allTasks.value
    .filter(task => task.assignee?._id === userId && task.status !== 'completed' && task.status !== 'cancelled')
    .slice(0, 3); // 只显示前3个
});

// Mock Data for Announcements (still mock for now)
const announcements = ref([
  { id: 1, content: '武林大会通知：本月十五，华山之巅，不见不散！', type: 'urgent' },
  { id: 2, content: '新秘籍《代码禅》已入库，欢迎借阅。', type: 'info' },
]);

// Fetch data on mount
onMounted(async () => {
  if (!userStore.userInfo && !userStore.loading) {
    await userStore.fetchUserInfo();
  }
  await attendanceStore.fetchAttendanceData();
  // **新增：获取任务列表**
  if (userStore.isLoggedIn) {
     await taskStore.fetchTasks(); // 获取所有任务，dashboard再筛选
  }
});


watch([clockInTime, clockOutTime, todayHours, clockedIn], () => {
  console.log("Dashboard: Attendance data changed in store.");
}, { deep: true });

// **新增：监听 userInfo 变化，重新获取任务**
watch(userInfo, (newVal, oldVal) => {
  if (newVal?._id !== oldVal?._id && newVal?._id) { // User logged in or switched
    taskStore.fetchTasks();
  } else if (!newVal?._id && oldVal?._id) { // User logged out
    taskStore.tasks = []; // Clear tasks on logout
  }
});


const getRealmTitle = (points) => {
  if (points === undefined || points === null) return '江湖小白';
  if (points < 100) return '初学乍练';
  if (points < 500) return '略有小成';
  if (points < 1000) return '驾轻就熟';
  if (points < 2000) return '融会贯通';
  if (points < 5000) return '一代宗师';
  return '深不可测';
};

const 江湖声望 = computed(() => {
    return userInfo.value?.stats?.totalHours || 0;
});

</script>

<template>
  <div class="dashboard-page">
    <div class="dashboard-grid">
      <!-- 今日修行 (打卡) -->
      <div class="card practice-card">
        <h2 class="card-title">今日修行</h2>
        <div class="practice-content">
          <div class="practice-visual">
            <span class="pixel-art-placeholder">🧘</span>
          </div>
          <div v-if="loadingAttendance" class="loading-text">卷宗载入中...</div>
          <div v-else-if="attendanceError" class="error-text">{{ attendanceError }}</div>
          <div v-else class="practice-actions">
            <button
              @click="handleClockAction('in')"
              class="clock-btn clock-in-btn"
              :disabled="!canClockIn"
            >
              ☀️ 早课入定
            </button>
            <button
              @click="handleClockAction('out')"
              class="clock-btn clock-out-btn"
              :disabled="!canClockOut"
            >
              🌙 晚课出定
            </button>
            <div v-if="clockedIn && clockOutTime" class="completed-message">
              今日修行圆满！
            </div>
          </div>
          <div class="practice-duration">
            修行时长：{{ 修行时长 }}
          </div>
          <div v-if="clockAnimationActive" class="clock-animation-feedback">✨🎉</div>
          <router-link to="/attendance" class="more-link mt-3">查看修行日志详情...</router-link>
        </div>
      </div>

      <!-- 功力境界 (个人信息) -->
      <div class="card realm-card">
        <h2 class="card-title">功力境界</h2>
        <div v-if="loadingUser" class="loading-text">侠士信息载入中...</div>
        <div v-else-if="userError" class="error-text">{{ userError }}</div>
        <div v-else-if="userInfo" class="realm-content">
          <div class="realm-profile">
            <img :src="fullAvatarUrl" alt="头像" class="realm-avatar" />
            <div class="realm-user-details">
              <p class="realm-nickname">{{ userInfo.nickname || userInfo.username }}</p>
              <p class="realm-title">{{ userInfo.position || getRealmTitle(userInfo.stats?.points) }}</p>
            </div>
          </div>
          <div class="realm-stats">
            <p>💰 内力值：{{ userInfo.stats?.points || 0 }}</p>
            <p>📜 江湖声望：{{ 江湖声望 }}</p>
          </div>
        </div>
        <div v-else class="loading-text">侠士信息载入中或尚未入世。</div>
      </div>

      <!-- 待办榜文 (任务预览) -->
      <div class="card todo-card">
        <h2 class="card-title">待办榜文</h2>
        <div v-if="loadingTasks" class="loading-text">榜文载入中...</div>
        <div v-else-if="tasksError" class="error-text">{{ tasksError }}</div>
        <ul v-else-if="pendingTasks.length > 0" class="todo-list">
          <li v-for="task in pendingTasks" :key="task._id" :class="getTaskStatusClass(task.status)">
            <span>{{ getTaskIcon(task.category) }} {{ task.title }}</span>
            <span class="task-reward-preview">💰 {{ task.rewardPoints }}</span>
            <span :class="getTaskStatusClass(task.status)" class="pixel-seal">{{ getTaskStatusText(task.status) }}</span>
          </li>
        </ul>
        <p v-else class="empty-text">今日无事，可静心参悟。</p>
        <router-link to="/tasks" class="more-link">查看更多榜文...</router-link>
      </div>

      <!-- 武林秘闻 (公告/动态) -->
      <div class="card news-card">
        <h2 class="card-title">武林秘闻</h2>
        <ul v-if="announcements.length > 0" class="news-list">
          <li v-for="newsItem in announcements.slice(0, 2)" :key="newsItem.id" :class="newsItem.type">
            {{ newsItem.content }}
          </li>
        </ul>
        <p v-else class="empty-text">江湖平静，暂无秘闻。</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss';

.dashboard-page {
  padding: 20px;
  background-color: var(--color-ancient-paper);
  font-family: 'Pixelify Sans', monospace;
  min-height: calc(100vh - 60px);
  position: relative;
}

.dashboard-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));

  @media (min-width: 992px) {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto;

    .practice-card { grid-area: 1 / 1 / 2 / 2; }
    .realm-card { grid-area: 1 / 2 / 2 / 3; }
    .todo-card { grid-area: 2 / 1 / 3 / 2; }
    .news-card { grid-area: 2 / 2 / 3 / 3; }
  }
}

.card {
  @extend .ancient-pixel-container; // 继承全局像素卡片样式
  padding: 20px; // 局部调整内边距
}

.card-title {
  font-size: 1.8em;
  color: var(--color-ancient-ink);
  margin-bottom: 15px;
  text-align: center;
  border-bottom: 2px dashed var(--color-ancient-light-brown);
  padding-bottom: 10px;
  font-family: 'ZCOOL KuaiLe', serif;
}

.loading-text, .empty-text, .error-text {
  text-align: center;
  color: var(--color-ancient-light-brown);
  padding: 10px 0;
  font-family: 'Noto Serif SC', serif;
}
.error-text {
  color: var(--color-ancient-blood-red);
}

/* 今日修行 (打卡) */
.practice-card {
  align-items: center;
  .practice-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
  .practice-visual {
    font-size: 4em;
    margin-bottom: 20px;
  }
  .practice-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: center;
  }
  .clock-btn {
    @extend .pixel-button; // 继承全局像素按钮样式
    padding: 10px 20px;
    font-size: 1.1em;
    // 重写颜色以适配这个模块
    background-color: var(--color-ancient-gold);
    color: var(--color-ancient-ink);
    border-color: var(--color-ancient-dark-brown);
    &:hover:not(:disabled) {
      background-color: var(--color-ancient-gold-light);
    }
  }
   .completed-message {
    font-size: 1.1em;
    color: var(--color-ancient-jade);
    font-weight: bold;
   }

  .practice-duration {
    font-size: 1.2em;
    color: var(--color-ancient-dark-brown);
    margin-top: 10px;
  }
  .clock-animation-feedback {
    font-size: 2em;
    position: absolute;
    animation: pop-fade 0.6s ease-out forwards;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
@keyframes pop-fade {
    0% { transform: scale(0.5); opacity: 0; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 0; }
}


/* 功力境界 (个人信息) */
.realm-card {
  .realm-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }
  .realm-profile {
    display: flex;
    align-items: center;
    gap: 15px;
  }
  .realm-avatar {
    width: 70px;
    height: 70px;
    border-radius: 0;
    border: 2px solid var(--color-ancient-dark-brown);
    object-fit: cover;
    background-color: var(--color-ancient-light-brown);
    image-rendering: pixelated;
  }
  .realm-user-details {
    .realm-nickname {
      font-size: 1.4em;
      font-weight: bold;
      color: var(--color-ancient-ink);
      font-family: 'ZCOOL KuaiLe', serif;
    }
    .realm-title {
      font-size: 1em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Noto Serif SC', serif;
    }
  }
  .realm-stats {
    font-size: 1.1em;
    color: var(--color-ancient-dark-brown);
    text-align: center;
    p {
      margin: 5px 0;
    }
  }
}

/* 待办榜文 (任务预览) */
.todo-card {
  .todo-list {
    list-style: none;
    padding: 0;
    width: 100%;
    li {
      background-color: var(--color-ancient-paper);
      border: 2px dashed var(--color-ancient-light-brown);
      padding: 10px;
      margin-bottom: 8px;
      border-radius: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 1.1em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Noto Serif SC', serif;
      &.status-completed { // Completed task in dashboard list
        text-decoration: line-through;
        color: #aaa;
        background-color: var(--color-ancient-stone-gray-light);
        border-color: var(--color-ancient-stone-gray);
        .pixel-seal {
          color: var(--color-ancient-jade);
        }
      }
      &.status-in-progress { // In-progress task
        background-color: var(--color-ancient-highlight);
        border-color: var(--color-ancient-jade);
        .pixel-seal {
          color: var(--color-ancient-jade-dark);
        }
      }
      &.status-pending { // Pending task
        background-color: var(--color-ancient-paper);
        border-color: var(--color-ancient-gold);
        .pixel-seal {
          color: var(--color-ancient-gold);
        }
      }
      .task-reward-preview {
        font-family: 'Pixelify Sans', monospace;
        font-weight: bold;
        color: var(--color-ancient-gold);
        margin-left: 10px;
      }
    }
  }
  .pixel-seal {
    font-family: 'KaiTi', '楷体', serif;
    font-size: 1.2em;
    border: 1px solid currentColor;
    padding: 0 2px;
    border-radius: 2px;
    margin-left: 10px;
  }
  .more-link {
    display: block;
    text-align: right;
    margin-top: 10px;
    color: var(--color-ancient-jade);
    text-decoration: none;
    font-family: 'Noto Serif SC', serif;
    &:hover {
      text-decoration: underline;
    }
  }
}

/* 武林秘闻 (公告/动态) */
.news-card {
  .news-list {
    list-style: none;
    padding: 0;
    width: 100%;
    li {
      background-color: var(--color-ancient-paper);
      border-left: 4px solid var(--color-ancient-gold);
      padding: 10px;
      margin-bottom: 8px;
      font-size: 1em;
      color: var(--color-ancient-dark-brown);
      font-family: 'Noto Serif SC', serif;
      &.urgent {
        border-left-color: var(--color-ancient-blood-red);
        font-weight: bold;
        font-family: 'ZCOOL KuaiLe', serif;
      }
      &.info {
        border-left-color: var(--color-ancient-jade);
      }
      &:last-child { margin-bottom: 0; }
    }
  }
}
</style>
```

---

**测试与验证步骤：**

1.  **启动后端：**
    *   进入 `backend` 目录。
    *   **重要：** `npm install` 安装新增的 `task.controller.js` 和 `task.routes.js` 所依赖的 `Task` 和 `User` 模型，以及可能新引入的包。
    *   运行 `npm run dev` 启动后端服务器。

2.  **启动前端：**
    *   进入 `frontend` 目录。
    *   **重要：** `npm install` 安装新增的 `task.js` store，特别是 Pinia 和 Axios 应该已经有了。
    *   运行 `npm run dev` 启动前端应用。

3.  **创建测试任务 (后端)**
    由于目前没有后端管理员界面来创建任务，我们可以通过以下方式手动创建几个测试任务：
    *   **方法一 (推荐): 使用 Postman/Insomnia/curl 调用 API**
        *   登录获取一个用户的 token。
        *   使用该 token (作为 `Bearer Token`) 发送 `POST` 请求到 `http://localhost:3001/api/tasks`。
        *   请求体 (JSON):
            ```json
            {
                "title": "撰写《摸鱼心法》真经",
                "description": "详细记录今日摸鱼心得和技巧，字数不少于1000字，附带截图为证。",
                "type": "official",
                "category": "document",
                "rewardPoints": 150,
                "isPublic": true,
                "dueDate": "2023-12-31T23:59:59Z"
            }
            ```
            可以创建多个任务，改变 `title` 和 `rewardPoints`。创建时 `creator` 会自动是当前登录用户的 ID。
    *   **方法二 (后端代码中临时添加):**
        *   在 `backend/src/server.js` 或 `app.js` 中临时添加一段代码，在数据库连接成功后执行，用于创建任务。**完成后请删除**。
            ```javascript
            // backend/src/server.js (临时添加，完成后删除)
            // ...
            const Task = require('./models/task.model'); // 引入 Task 模型
            // const User = require('./models/user.model'); // 如果需要指定 creator/assignee

            const createInitialTasks = async () => {
                const existingTasks = await Task.countDocuments();
                if (existingTasks === 0) {
                    console.log('检测到无任务，创建初始测试任务...');
                    // 注意: 这里creator和assignee需要有效的userId，请手动替换
                    // 可以先去MongoDB查一个用户ID
                    const testUserId = 'YOUR_EXISTING_USER_ID_FROM_MONGO'; // <-- 替换为你数据库中存在的用户ID

                    if (!testUserId) {
                        console.warn('!!! 警告: 未提供有效的测试用户ID，无法创建初始任务。请手动替换 testUserId !!!');
                        return;
                    }

                    await Task.create([
                        {
                            title: '撰写《摸鱼心法》初稿',
                            description: '详细记录今日摸鱼心得和技巧，字数不少于500字。',
                            rewardPoints: 50,
                            type: 'personal',
                            category: 'document',
                            isPublic: true,
                            creator: testUserId,
                            status: 'pending'
                        },
                        {
                            title: '设计江湖茶馆海报',
                            description: '为江湖茶馆设计一张像素风格海报，要求体现古风与武侠元素。',
                            rewardPoints: 80,
                            type: 'official',
                            category: 'design',
                            isPublic: true,
                            creator: testUserId,
                            status: 'pending'
                        },
                        {
                            title: '勘探秘境：数据森林',
                            description: '探索数据森林中的奇珍异草，带回至少3种稀有数据样本。',
                            rewardPoints: 120,
                            type: 'official',
                            category: 'work',
                            isPublic: true,
                            creator: testUserId,
                            status: 'pending'
                        },
                        {
                            title: '挑战：连续打卡七天',
                            description: '保持七天不间断的早晚课，证明您的修行毅力。',
                            rewardPoints: 30,
                            type: 'official',
                            category: 'study',
                            isPublic: false, // 这是一个非公开的挑战任务
                            creator: testUserId,
                            status: 'pending'
                        }
                    ]);
                    console.log('初始任务创建成功。');
                }
            };
            // 在 connectDB().then() 之后调用
            startServer().then(() => {
                createInitialTasks(); // 在服务器启动后调用
            });
            // ...
            ```

4.  **前端页面测试：**
    *   **`Dashboard` 页面：**
        *   查看“待办榜文”卡片，确认是否显示了你创建的“我领取的”和“待领取的”任务（最多3条）。
        *   确认任务标题、奖励积分和状态显示正确。
    *   **`Tasks` 页面 (`/tasks`)：**
        *   **任务列表：**
            *   确认页面加载时，显示了所有公开任务（以及你自己创建的非公开任务，如果你是管理员）。
            *   每张任务卡片应显示标题、描述、奖励内力值、状态（待领取/进行中/已完成/已取消）、对应的任务图标。
            *   任务卡片根据状态有不同的颜色边框和图标颜色。
        *   **筛选功能：**
            *   点击“全部榜文”、“门派推荐”、“我领取的”标签，确认任务列表能正确筛选。
            *   “我领取的”应该只显示当前登录用户已领取（`in_progress`）和已完成（`completed`）的任务。
            *   “门派推荐”应该显示 `isPublic: true` 的任务。
        *   **任务领取：**
            *   点击一个“待领取”的任务卡片，会弹出任务详情模态框。
            *   在模态框中，点击“领取榜文”按钮。
            *   确认按钮变为“领取中...”，然后变为“完成榜文”，模态框中的状态也更新为“进行中”。
            *   关闭模态框，在任务列表页确认该任务的状态已更新。
            *   **重新刷新页面，验证状态是否持久化。**
        *   **任务完成：**
            *   点击你已经领取（状态为“进行中”）的任务卡片，弹出模态框。
            *   在模态框中，点击“完成榜文”按钮。
            *   确认按钮变为“完成中...”，然后变为“已完成”，模态框中的状态也更新为“已完成”。
            *   同时，观察用户头像旁的“内力值”是否增加（`Dashboard` 页面或刷新 `Workspace` 页面查看）。
            *   关闭模态框，在任务列表页确认该任务的状态已更新。
            *   **重新刷新页面，验证状态和积分是否持久化。**
        *   **权限验证：** 尝试领取已被他人领取的任务，或完成非分配给自己的任务，后端应返回错误消息并在前端显示。

这个任务模块的实现是一个重要的里程碑，它为用户提供了核心的游戏化体验，并直接与“内力值”这一激励系统挂钩。